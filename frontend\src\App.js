import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Navbar from './components/Navbar';
import HomePage from './pages/HomePage';

// Placeholder components for now
const CarListPage = () => <div className="p-8"><h1 className="text-2xl">Car List - Coming Soon</h1></div>;
const LoginPage = () => <div className="p-8"><h1 className="text-2xl">Login - Coming Soon</h1></div>;
const RegisterPage = () => <div className="p-8"><h1 className="text-2xl">Register - Coming Soon</h1></div>;

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <main>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/cars" element={<CarListPage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="*" element={
                <div className="flex justify-center items-center h-64">
                  <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                    <p className="text-gray-600">Page not found</p>
                  </div>
                </div>
              } />
            </Routes>
          </main>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\car rental\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport Navbar from './components/Navbar';\nimport HomePage from './pages/HomePage';\n\n// Placeholder components for now\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CarListPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n    className: \"text-2xl\",\n    children: \"Car List - Coming Soon\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 48\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 27\n}, this);\n_c = CarListPage;\nconst LoginPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n    className: \"text-2xl\",\n    children: \"Login - Coming Soon\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 46\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 9,\n  columnNumber: 25\n}, this);\n_c2 = LoginPage;\nconst RegisterPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n    className: \"text-2xl\",\n    children: \"Register - Coming Soon\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 49\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 28\n}, this);\n_c3 = RegisterPage;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cars\",\n              element: /*#__PURE__*/_jsxDEV(CarListPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                    children: \"404\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 27,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Page not found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 28,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 26,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"CarListPage\");\n$RefreshReg$(_c2, \"LoginPage\");\n$RefreshReg$(_c3, \"RegisterPage\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "HomePage", "jsxDEV", "_jsxDEV", "CarListPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "LoginPage", "_c2", "RegisterPage", "_c3", "App", "path", "element", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/car rental/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport Navbar from './components/Navbar';\nimport HomePage from './pages/HomePage';\n\n// Placeholder components for now\nconst CarListPage = () => <div className=\"p-8\"><h1 className=\"text-2xl\">Car List - Coming Soon</h1></div>;\nconst LoginPage = () => <div className=\"p-8\"><h1 className=\"text-2xl\">Login - Coming Soon</h1></div>;\nconst RegisterPage = () => <div className=\"p-8\"><h1 className=\"text-2xl\">Register - Coming Soon</h1></div>;\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"min-h-screen bg-gray-50\">\n          <Navbar />\n          <main>\n            <Routes>\n              <Route path=\"/\" element={<HomePage />} />\n              <Route path=\"/cars\" element={<CarListPage />} />\n              <Route path=\"/login\" element={<LoginPage />} />\n              <Route path=\"/register\" element={<RegisterPage />} />\n              <Route path=\"*\" element={\n                <div className=\"flex justify-center items-center h-64\">\n                  <div className=\"text-center\">\n                    <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">404</h1>\n                    <p className=\"text-gray-600\">Page not found</p>\n                  </div>\n                </div>\n              } />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,QAAQ,MAAM,kBAAkB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAGA,CAAA,kBAAMD,OAAA;EAAKE,SAAS,EAAC,KAAK;EAAAC,QAAA,eAACH,OAAA;IAAIE,SAAS,EAAC,UAAU;IAAAC,QAAA,EAAC;EAAsB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAK,CAAC;AAACC,EAAA,GAApGP,WAAW;AACjB,MAAMQ,SAAS,GAAGA,CAAA,kBAAMT,OAAA;EAAKE,SAAS,EAAC,KAAK;EAAAC,QAAA,eAACH,OAAA;IAAIE,SAAS,EAAC,UAAU;IAAAC,QAAA,EAAC;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAK,CAAC;AAACG,GAAA,GAA/FD,SAAS;AACf,MAAME,YAAY,GAAGA,CAAA,kBAAMX,OAAA;EAAKE,SAAS,EAAC,KAAK;EAAAC,QAAA,eAACH,OAAA;IAAIE,SAAS,EAAC,UAAU;IAAAC,QAAA,EAAC;EAAsB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAK,CAAC;AAACK,GAAA,GAArGD,YAAY;AAElB,SAASE,GAAGA,CAAA,EAAG;EACb,oBACEb,OAAA,CAACJ,YAAY;IAAAO,QAAA,eACXH,OAAA,CAACP,MAAM;MAAAU,QAAA,eACLH,OAAA;QAAKE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCH,OAAA,CAACH,MAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVP,OAAA;UAAAG,QAAA,eACEH,OAAA,CAACN,MAAM;YAAAS,QAAA,gBACLH,OAAA,CAACL,KAAK;cAACmB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEf,OAAA,CAACF,QAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCP,OAAA,CAACL,KAAK;cAACmB,IAAI,EAAC,OAAO;cAACC,OAAO,eAAEf,OAAA,CAACC,WAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDP,OAAA,CAACL,KAAK;cAACmB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEf,OAAA,CAACS,SAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CP,OAAA,CAACL,KAAK;cAACmB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEf,OAAA,CAACW,YAAY;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDP,OAAA,CAACL,KAAK;cAACmB,IAAI,EAAC,GAAG;cAACC,OAAO,eACrBf,OAAA;gBAAKE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpDH,OAAA;kBAAKE,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BH,OAAA;oBAAIE,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DP,OAAA;oBAAGE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACS,GAAA,GA1BQH,GAAG;AA4BZ,eAAeA,GAAG;AAAC,IAAAL,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}